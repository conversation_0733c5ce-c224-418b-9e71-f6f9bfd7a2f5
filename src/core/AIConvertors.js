import dotenv from 'dotenv';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText } from 'ai';
import { LLM_MODEL_CONFIG } from '../../config.js';
import {
  generateHTMLToMarkdownPrompt,
  generateMarkdownToMarkdownPrompt,
} from '../utils/AIPrompts.js';
import { handleError } from '../utils/GlobalErrorHandler.js';
import { createAIProgressLoader } from '../utils/AIProgressLoader.js';

dotenv.config({
  quiet: true,
});

/**
 * Convert HTML to markdown using AI with progress indicators
 * @param {string} htmlContent - The HTML content to convert
 * @param {string} userQuery - Optional user query for specific content focus
 * @param {Object} streamingService - Optional streaming service for progress updates
 * @returns {Promise<string>} - The AI-converted markdown content
 */
export async function convertAndImproveMarkdownFromHTML(
  htmlContent,
  userQuery = '',
  streamingService = null
) {
  if (!htmlContent.trim()) {
    return '';
  }

  // Initialize AI Progress Loader
  const aiLoader = createAIProgressLoader(streamingService);

  // Initialize Google AI
  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_AI_API_KEY,
  });

  let streamingComplete = false;
  let convertedMarkdown = '';

  try {
    const { textStream } = await streamText({
      model: google(LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash'),
      temperature: 0.9,
      providerOptions: {
        google: {
          thinkingConfig: {
            thinkingBudget: 1024,
          },
        },
      },
      prompt: generateHTMLToMarkdownPrompt(userQuery, htmlContent),
      onFinish: result => {
        streamingComplete = true;
        convertedMarkdown = result.text ?? 'AI no data error';
      },
    });

    // Process the streaming response without showing text in CLI
    for await (const _ of textStream) {
      console.log('🤖 AI Analysis generating markdown response...');
    }

    while (!streamingComplete) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!streamingComplete) {
      aiLoader.log('AI streaming timeout, using partial result', 'warn');
    }

    if (!convertedMarkdown.trim()) {
      aiLoader.errorOperation(
        new Error('AI returned empty markdown'),
        '⚠️ AI returned empty markdown'
      );
      return '';
    }

    // Complete the AI operation
    aiLoader.completeOperation(
      `✅ HTML to Markdown conversion complete! Generated ${convertedMarkdown.length} characters`,
      {
        outputLength: convertedMarkdown.length,
        inputLength: htmlContent.length,
      }
    );

    return convertedMarkdown;
  } catch (error) {
    await handleError(error, {
      operation: 'convertAndImproveMarkdown',
      htmlLength: htmlContent.length,
      userQuery: userQuery || 'none',
      modelUsed: LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash',
    });

    // Return empty string if AI fails
    return '';
  }
}

/**
 * Convert Markdown to markdown using AI with progress indicators
 * @param {string} markdownContent - The markdown content to convert
 * @param {string} userQuery - Optional user query for specific content focus
 * @param {Object} streamingService - Optional streaming service for progress updates
 * @returns {Promise<string>} - The AI-converted markdown content
 */
export async function convertAndImproveMarkdownFromMarkdown(
  markdownContent,
  userQuery = '',
  streamingService = null
) {
  if (!markdownContent.trim()) {
    return '';
  }

  // Initialize AI Progress Loader
  const aiLoader = createAIProgressLoader(streamingService);

  // Initialize Google AI
  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_AI_API_KEY,
  });

  let streamingComplete = false;
  let convertedMarkdown = '';

  try {
    // Start AI operation with progress indicators
    aiLoader.startOperation(
      'converting',
      'Starting Markdown improvement with AI',
      'processing'
    );

    const { textStream } = await streamText({
      model: google(LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash'),
      temperature: 0.9,
      providerOptions: {
        google: {
          thinkingConfig: {
            thinkingBudget: 1024,
          },
        },
      },
      prompt: generateMarkdownToMarkdownPrompt(userQuery, markdownContent),
      onFinish: result => {
        streamingComplete = true;
        convertedMarkdown = result.text ?? 'AI no data error';
      },
    });

    // Process the streaming response without showing text in CLI
    for await (const _ of textStream) {
      console.log('🤖 AI Analysis generating markdown response...');
    }

    while (!streamingComplete) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (!streamingComplete) {
      aiLoader.log('AI streaming timeout, using partial result', 'warn');
    }

    if (!convertedMarkdown.trim()) {
      aiLoader.errorOperation(
        new Error('AI returned empty Markdown'),
        '⚠️ AI returned empty Markdown'
      );
      return '';
    }

    // Complete the AI operation
    aiLoader.completeOperation(
      `✅ Markdown improvement complete! Generated ${convertedMarkdown.length} characters`,
      {
        outputLength: convertedMarkdown.length,
        inputLength: markdownContent.length,
      }
    );

    return convertedMarkdown;
  } catch (error) {
    aiLoader.errorOperation(
      error,
      `AI Markdown improvement failed: ${error.message}`
    );

    await handleError(error, {
      operation: 'convertAndImproveMarkdown',
      markdownLength: markdownContent.length,
      userQuery: userQuery || 'none',
      modelUsed: LLM_MODEL_CONFIG.smallModel ?? 'gemini-2.5-flash',
    });

    // Return empty string if AI fails
    return '';
  }
}
