/**
 * SimpleLogger - A simplified logging utility that consolidates essential logging features
 * Replaces the complex StreamingService, AIProgressLoader, and GlobalErrorHandler with a cleaner approach
 */

// Simple error categories for basic classification
export const ERROR_TYPES = {
  NETWORK: 'network',
  BROWSER: 'browser',
  AI: 'ai',
  FILE: 'file',
  TIMEOUT: 'timeout',
  RATE_LIMIT: 'rate_limit',
  UNKNOWN: 'unknown',
};

// Log levels
export const LOG_LEVELS = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
};

/**
 * Simple Logger Class
 */
export class SimpleLogger {
  constructor(options = {}) {
    this.enableStreaming = options.enableStreaming || false;
    this.progressCallback = options.progressCallback || null;
    this.currentPhase = null;
    this.startTime = Date.now();
    this.errorCount = 0;
  }

  /**
   * Log a simple message
   */
  log(message, level = LOG_LEVELS.INFO) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = this._getEmoji(level);

    console.log(`${emoji} [${timestamp}] ${message}`);

    // Stream if enabled
    if (this.enableStreaming && this.progressCallback) {
      this.progressCallback({
        type: 'log',
        level,
        message,
        timestamp: new Date().toISOString(),
        phase: this.currentPhase,
      });
    }
  }

  /**
   * Log info message
   */
  info(message) {
    this.log(message, LOG_LEVELS.INFO);
  }

  /**
   * Log warning message
   */
  warn(message) {
    this.log(message, LOG_LEVELS.WARN);
  }

  /**
   * Log error message and handle error
   */
  error(message, error = null) {
    this.errorCount++;
    this.log(message, LOG_LEVELS.ERROR);

    if (error) {
      // Simple error classification
      const errorType = this._classifyError(error);
      const shouldRetry = this._shouldRetry(errorType);

      console.log(
        `   Error Type: ${errorType}${shouldRetry ? ' (retryable)' : ''}`
      );

      if (error.stack) {
        // Show only first 2 lines of stack for brevity
        const stackLines = error.stack.split('\n').slice(0, 2);
        console.log(`   Stack: ${stackLines.join(' → ')}`);
      }

      // Stream error if enabled
      if (this.enableStreaming && this.progressCallback) {
        this.progressCallback({
          type: 'error',
          message,
          errorType,
          shouldRetry,
          phase: this.currentPhase,
          timestamp: new Date().toISOString(),
        });
      }

      return { errorType, shouldRetry };
    }
  }

  /**
   * Start a new phase
   */
  startPhase(phaseName, description = '') {
    this.currentPhase = phaseName;
    const message = description ? `${phaseName}: ${description}` : phaseName;
    this.log(`🚀 Starting ${message}`);

    if (this.enableStreaming && this.progressCallback) {
      this.progressCallback({
        type: 'phase_start',
        phase: phaseName,
        description,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * End current phase
   */
  endPhase(result = {}) {
    if (this.currentPhase) {
      this.log(`✅ Completed ${this.currentPhase}`);

      if (this.enableStreaming && this.progressCallback) {
        this.progressCallback({
          type: 'phase_end',
          phase: this.currentPhase,
          result,
          timestamp: new Date().toISOString(),
        });
      }

      this.currentPhase = null;
    }
  }

  /**
   * Update progress within current phase
   */
  updateProgress(message) {
    if (this.currentPhase) {
      this.log(`⚡ ${message}`);

      if (this.enableStreaming && this.progressCallback) {
        this.progressCallback({
          type: 'progress',
          phase: this.currentPhase,
          message,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * Complete the entire process
   */
  complete(finalMessage = 'Process completed successfully') {
    const elapsed = Date.now() - this.startTime;
    const duration = this._formatDuration(elapsed);

    this.log(`🎉 ${finalMessage} (${duration})`);

    if (this.errorCount > 0) {
      this.log(
        `⚠️ Completed with ${this.errorCount} error(s)`,
        LOG_LEVELS.WARN
      );
    }

    if (this.enableStreaming && this.progressCallback) {
      this.progressCallback({
        type: 'complete',
        message: finalMessage,
        duration,
        errorCount: this.errorCount,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Simple error classification
   */
  _classifyError(error) {
    const errorString = error.toString().toLowerCase();

    if (
      errorString.includes('network') ||
      errorString.includes('timeout') ||
      errorString.includes('connection')
    ) {
      return ERROR_TYPES.NETWORK;
    }
    if (
      errorString.includes('browser') ||
      errorString.includes('playwright') ||
      errorString.includes('page')
    ) {
      return ERROR_TYPES.BROWSER;
    }
    if (
      errorString.includes('ai') ||
      errorString.includes('gemini') ||
      errorString.includes('model')
    ) {
      return ERROR_TYPES.AI;
    }
    if (
      errorString.includes('file') ||
      errorString.includes('enoent') ||
      errorString.includes('eacces')
    ) {
      return ERROR_TYPES.FILE;
    }
    if (
      errorString.includes('quota') ||
      errorString.includes('rate limit') ||
      errorString.includes('429')
    ) {
      return ERROR_TYPES.RATE_LIMIT;
    }

    return ERROR_TYPES.UNKNOWN;
  }

  /**
   * Determine if error should be retried
   */
  _shouldRetry(errorType) {
    return [
      ERROR_TYPES.NETWORK,
      ERROR_TYPES.BROWSER,
      ERROR_TYPES.TIMEOUT,
      ERROR_TYPES.RATE_LIMIT,
    ].includes(errorType);
  }

  /**
   * Get emoji for log level
   */
  _getEmoji(level) {
    switch (level) {
      case LOG_LEVELS.INFO:
        return 'ℹ️';
      case LOG_LEVELS.WARN:
        return '⚠️';
      case LOG_LEVELS.ERROR:
        return '❌';
      default:
        return 'ℹ️';
    }
  }

  /**
   * Format duration in human readable format
   */
  _formatDuration(ms) {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  /**
   * Get current stats
   */
  getStats() {
    return {
      currentPhase: this.currentPhase,
      elapsed: Date.now() - this.startTime,
      errorCount: this.errorCount,
      isStreaming: this.enableStreaming,
    };
  }
}

/**
 * Create a simple logger instance
 */
export function createLogger(options = {}) {
  return new SimpleLogger(options);
}

/**
 * Global logger instance for simple usage
 */
export const logger = new SimpleLogger();

/**
 * Convenience functions
 */
export function log(message) {
  logger.log(message);
}
export function info(message) {
  logger.info(message);
}
export function warn(message) {
  logger.warn(message);
}
export function error(message, err = null) {
  return logger.error(message, err);
}
export function startPhase(name, desc) {
  logger.startPhase(name, desc);
}
export function endPhase(result) {
  logger.endPhase(result);
}
export function updateProgress(message) {
  logger.updateProgress(message);
}
export function complete(message) {
  logger.complete(message);
}
