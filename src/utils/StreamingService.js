import { STREAMING_CONFIG } from '../../config.js';
import { createLogger } from './SimpleLogger.js';

/**
 * Simplified StreamingService class for progress streaming
 */
export class StreamingService {
  constructor(progressCallback = null) {
    this.progressCallback = progressCallback;
    this.isStreaming = progressCallback !== null;
    this.logger = createLogger({
      enableStreaming: this.isStreaming,
      progressCallback: this.progressCallback,
    });
    this.heartbeatInterval = null;

    // Start heartbeat if streaming is enabled
    if (this.isStreaming) {
      this.startHeartbeat();
    }
  }

  /**
   * Start a new phase of the scraping process
   * @param {string} phaseName - Name of the phase
   * @param {string} description - Description of what's happening
   */
  startPhase(phaseName, description = '') {
    this.logger.startPhase(phaseName, description);
  }

  /**
   * Update progress within the current phase
   * @param {Object} update - Progress update object
   */
  updateProgress(update) {
    const message = update.message || 'Processing...';
    this.logger.updateProgress(message);
  }

  /**
   * End the current phase
   * @param {Object} result - Phase completion result
   */
  endPhase(result = {}) {
    this.logger.endPhase(result);
  }

  /**
   * Stream an error
   * @param {Error} error - Error object
   * @param {Object} context - Additional context
   */
  streamError(error) {
    this.logger.error(`Error: ${error.message}`, error);
  }

  /**
   * Log a message with streaming
   * @param {string} message - Log message
   * @param {string} level - Log level (info, warn, error)
   */
  log(message, level = 'info') {
    if (level === 'error') {
      this.logger.error(message);
    } else if (level === 'warn') {
      this.logger.warn(message);
    } else {
      this.logger.info(message);
    }
  }

  /**
   * Stream progress data (simplified)
   * @param {Object} data - Progress data to stream
   */
  streamProgress(data) {
    if (!this.isStreaming || !this.progressCallback) {
      return;
    }

    try {
      const streamData = {
        timestamp: new Date().toISOString(),
        ...data,
      };
      this.progressCallback(streamData);
    } catch (error) {
      console.error('Error streaming progress:', error);
    }
  }

  /**
   * Send a heartbeat to keep the stream alive
   */
  sendHeartbeat() {
    this.streamProgress({
      type: 'heartbeat',
      alive: true,
    });
  }

  /**
   * Start the heartbeat interval
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, STREAMING_CONFIG.heartbeatInterval || 30000);
  }

  /**
   * Stop the heartbeat interval
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Complete the streaming process
   * @param {Object} finalResult - Final result data
   */
  complete(finalResult = {}) {
    this.logger.complete('Streaming completed');
    this.stopHeartbeat();

    this.streamProgress({
      type: 'stream_complete',
      ...finalResult,
    });
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopHeartbeat();
    this.progressCallback = null;
  }

  /**
   * Get streaming statistics
   * @returns {Object} Streaming statistics
   */
  getStats() {
    return {
      isStreaming: this.isStreaming,
      hasHeartbeat: this.heartbeatInterval !== null,
      ...this.logger.getStats(),
    };
  }
}

/**
 * Create a new streaming service instance
 * @param {Function} progressCallback - Progress callback function
 * @returns {StreamingService} New streaming service instance
 */
export function createStreamingService(progressCallback = null) {
  return new StreamingService(progressCallback);
}
