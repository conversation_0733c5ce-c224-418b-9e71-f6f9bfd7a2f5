import dotenv from 'dotenv';
import { MAX_RETRY_COUNT, RETRY_DELAY } from '../config.js';

// Import all the function modules
import {
  closeBrowser,
  getBrowser,
  getPage,
  initBrowser,
} from './browser-ops/BrowserManager.js';
import { navigateToUrl } from './browser-ops/PageNavigator.js';
import infiniteScrollUntilNoMoreNewNetworkRequest from './browser-ops/PageScroller.js';
import {
  convertAndImproveMarkdownFromHTML,
  convertAndImproveMarkdownFromMarkdown,
} from './core/AIConvertors.js';
import { combineContent } from './core/ContentCombiner.js';
import {
  convertToMarkdown,
  fixAndFormatHTML,
} from './core/ContentConvertor.js';
import { handleIframes } from './core/IframesHandler.js';
import {
  findInteractiveElements,
  processInteractiveElementsContent,
} from './core/InteractiveElementProcessor.js';
import { saveToFile } from './utils/FileManager.js';
import {
  ERROR_CATEGORIES,
  getRetryConfig,
  handleError,
} from './utils/GlobalErrorHandler.js';
import { createLogger } from './utils/SimpleLogger.js';
import { SimplePerformanceMonitor } from './utils/SimplePerformanceMonitor.js';
import { createStreamingService } from './utils/StreamingService.js';

dotenv.config({
  quiet: true,
});

// Scraping modes constants
export const SCRAPING_MODES = {
  NORMAL: 'normal',
  BEAST: 'beast',
};

/**
 * Create scraping context with shared state and utilities
 * @returns {Object} Scraping context with shared utilities
 */
function createScrapingContext() {
  return {
    currentMode: SCRAPING_MODES.BEAST,
    browser: null,
    page: null,
    performanceMonitor: new SimplePerformanceMonitor(),
    logger: createLogger(),
  };
}

/**
 * Process scraping in normal mode (simple extraction) with optional streaming support
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 * @param {string} userQuery - Optional user query for focused content extraction
 * @returns {Object} Processing result with enhancedError, markdown, and html
 */
async function processNormalMode(context, outputHtmlFilename, userQuery) {
  const streamingService = context.streamingService;

  // Start phase with streaming support
  if (streamingService) {
    streamingService.startPhase(
      'content-extraction',
      'Extracting and processing content in normal mode'
    );
  }
  context.performanceMonitor.startPhase('content-extraction');

  // Clean up the page content - same logic as beast mode for consistency
  await context.page.evaluate(() => {
    document
      .querySelectorAll("style, link, script[src^='https://']")
      .forEach(element => element.remove());
  });

  if (streamingService) {
    streamingService.updateProgress({
      message: 'Cleaning up page content and extracting HTML',
    });
  }

  const rawHTML = await context.page.content();
  await closeBrowser();

  if (streamingService) {
    streamingService.updateProgress({ message: 'Converting HTML to markdown' });
  }

  const cleanedHTML = await fixAndFormatHTML(rawHTML);

  // Save HTML file only if outputHtmlFilename is provided (for backward compatibility)
  if (outputHtmlFilename) {
    await saveToFile(cleanedHTML, `${outputHtmlFilename}.html`);
  }

  const rawMarkdown = await convertToMarkdown(cleanedHTML);
  let finalMarkdown = rawMarkdown;

  if (userQuery) {
    if (streamingService) {
      streamingService.updateProgress({
        message: 'Improving markdown with AI based on user query',
      });
    }
    finalMarkdown = await convertAndImproveMarkdownFromMarkdown(
      rawMarkdown,
      userQuery
    );

    // Save improved markdown file only if outputHtmlFilename is provided
    if (outputHtmlFilename) {
      await saveToFile(finalMarkdown, `${outputHtmlFilename}.md`);
    }
  } else if (outputHtmlFilename) {
    // Save raw markdown file only if outputHtmlFilename is provided
    await saveToFile(rawMarkdown, `${outputHtmlFilename}.raw.md`);
  }

  context.performanceMonitor.endPhase();

  if (streamingService) {
    streamingService.endPhase({
      success: true,
      markdownLength: finalMarkdown?.length || 0,
      htmlLength: cleanedHTML?.length || 0,
    });
  }

  return {
    enhancedError: null,
    markdown: finalMarkdown,
    html: cleanedHTML,
  };
}

/**
 * Process scraping in beast mode (advanced extraction with interactive elements) with optional streaming support
 * @param {Object} context - Scraping context
 * @param {string} outputHtmlFilename - Output filename
 * @param {string} userQuery - Optional user query for focused content extraction
 * @returns {Object} Processing result with enhancedError, markdown, and html
 */
async function processBeastMode(context, outputHtmlFilename, userQuery) {
  const streamingService = context.streamingService;
  let enhancedError = null;
  let dynamicContents = { contents: [] };

  // Step 1: Find interactive elements that might reveal hidden content
  if (streamingService) {
    streamingService.startPhase(
      'ai-element-detection',
      'Using AI to detect interactive elements'
    );
  }
  context.performanceMonitor.startPhase('AI element detection');

  // Clone HTML content without script tags for AI analysis - improved version with better error handling
  if (streamingService) {
    streamingService.log('📄 Starting HTML cloning process...', 'info');
  }

  let clonedHTML;
  try {
    clonedHTML = await context.page.evaluate(() => {
      try {
        console.log('🌐 Browser: Starting document cloning...');

        // Use document.documentElement instead of document.cloneNode(true)
        // because document.cloneNode(true) doesn't include the HTML element itself
        const sourceElement = document.documentElement || document.body;
        if (!sourceElement) {
          console.error('Browser: No documentElement or body found');
          return null;
        }

        console.log('🌐 Browser: Cloning documentElement...');
        const clonedPage = sourceElement.cloneNode(true);

        if (!clonedPage) {
          console.error('Browser: Cloning returned null');
          return null;
        }

        console.log(
          '🌐 Browser: Document cloned successfully, removing script/link elements...'
        );

        const scriptsAndLinks = clonedPage.querySelectorAll('script, link');
        scriptsAndLinks.forEach((element, index) => {
          try {
            element.remove();
          } catch (removeError) {
            console.warn(`Failed to remove element ${index}:`, removeError);
          }
        });

        const allElements = clonedPage.querySelectorAll('*');

        allElements.forEach((element, index) => {
          try {
            if (
              element.tagName === 'IFRAME' ||
              element.tagName === 'CODE' ||
              element.tagName === 'PRE'
            ) {
              return;
            }

            if (element.tagName !== 'TABLE' && !element.closest('table')) {
              const attrs = Array.from(element.attributes);
              attrs.forEach(attr => {
                try {
                  if (
                    attr.name !== 'class' &&
                    attr.name !== 'id' &&
                    !attr.name.startsWith('data-') &&
                    !attr.name.startsWith('aria-')
                  ) {
                    element.removeAttribute(attr.name);
                  }
                } catch (attrError) {
                  console.warn(
                    `Failed to remove attribute ${attr.name}:`,
                    attrError
                  );
                }
              });
            }
          } catch (elementError) {
            console.warn(`Failed to process element ${index}:`, elementError);
          }
        });

        // Generate the final HTML
        let result;
        if (clonedPage.outerHTML) {
          result = clonedPage.outerHTML;
        } else if (clonedPage.innerHTML) {
          // Fallback: wrap in HTML tags
          result = `<html>${clonedPage.innerHTML}</html>`;
        } else {
          console.error('Browser: No outerHTML or innerHTML available');
          return null;
        }

        console.log(
          '🌐 Browser: HTML generation complete, length:',
          result?.length || 0
        );
        console.log(
          '🌐 Browser: HTML preview:',
          result?.substring(0, 200) || 'no content'
        );
        return result;
      } catch (browserError) {
        console.error(
          'Browser: Error in cloning process:',
          browserError.message
        );
        console.error(
          'Browser: Error stack:',
          browserError.stack?.split('\n').slice(0, 3).join('\n')
        );

        // Emergency fallback: just get the body content
        try {
          console.log('🌐 Browser: Attempting emergency fallback...');
          const bodyContent = document.body ? document.body.innerHTML : '';
          console.log(
            '🌐 Browser: Emergency fallback length:',
            bodyContent.length
          );
          return bodyContent
            ? `<html><body>${bodyContent}</body></html>`
            : null;
        } catch (fallbackError) {
          console.error(
            'Browser: Emergency fallback also failed:',
            fallbackError.message
          );
          console.error(
            'Fallback error details:',
            fallbackError.stack?.split('\n').slice(0, 3).join('\n')
          );
          return null;
        }
      }
    });
  } catch (evaluateError) {
    if (streamingService) {
      streamingService.log(
        `❌ HTML cloning failed: ${evaluateError.message}`,
        'error'
      );
    }

    // Final fallback: get page content directly
    try {
      if (streamingService) {
        streamingService.log(
          '🔄 Attempting final fallback with page.content()...',
          'info'
        );
      }
      clonedHTML = await context.page.content();
      if (streamingService) {
        streamingService.log(
          `📄 Final fallback successful, length: ${clonedHTML?.length || 0}`,
          'info'
        );
      }
    } catch (finalError) {
      if (streamingService) {
        streamingService.log(
          `❌ Final fallback also failed: ${finalError.message}`,
          'error'
        );
      }
      clonedHTML = '';
    }
  }

  if (streamingService) {
    streamingService.updateProgress({
      message: 'Analyzing page structure with AI to find interactive elements',
    });
  }

  let interactiveElements;
  if (clonedHTML.length > 800000) {
    // Greater than 800K characters, skip AI analysis and fall back to normal mode (for gemini-2.5-flash it's safe to have 1M Context lenght, but to avoid extra junki websites, we are using 800K as a safe limit)
    if (streamingService) {
      streamingService.log(
        `🔄 Skipping AI analysis for large HTML content, HTML length: ${clonedHTML.length} characters`,
        'info'
      );
    }
    console.log(
      `🔄 Skipping AI analysis for large HTML content, HTML length: ${clonedHTML.length} characters`
    );
    context.performanceMonitor.endPhase();
    return await processNormalMode(context, outputHtmlFilename, userQuery);
  } else {
    if (streamingService) {
      streamingService.log(
        `📄 Analyzing ${clonedHTML.length} characters of HTML content`,
        'info'
      );
    }

    try {
      interactiveElements = await findInteractiveElements(
        clonedHTML,
        userQuery,
        streamingService
      );
    } catch (error) {
      const handledError = await handleError(error, {
        operation: 'findInteractiveElements',
        url: context.page?.url?.() || 'unknown',
        userQuery,
      });

      if (!handledError.shouldRetry) {
        enhancedError = handledError;
        interactiveElements = { elements: [] };
      } else {
        throw handledError; // Allow retry for retryable errors
      }
    }
  }

  context.performanceMonitor.endPhase();
  if (streamingService) {
    streamingService.endPhase({
      success: true,
      elementsFound: interactiveElements.elements?.length || 0,
    });
  }

  // Step 2: Process interactive elements to reveal dynamic content
  if (streamingService) {
    streamingService.startPhase(
      'dynamic-content-extraction',
      'Processing interactive elements to reveal hidden content'
    );
  }
  context.performanceMonitor.startPhase('interactive elements processing');

  if (interactiveElements.elements.length > 0) {
    try {
      if (streamingService) {
        streamingService.updateProgress({
          message: `Processing ${interactiveElements.elements.length} interactive elements`,
        });
      }

      dynamicContents = await processInteractiveElementsContent(
        context.page,
        interactiveElements
      );
    } catch (error) {
      const handledError = await handleError(error, {
        operation: 'processInteractiveElementsContent',
        url: context.page?.url?.() || 'unknown',
        elementsCount: interactiveElements.elements.length,
        userQuery,
      });

      if (!handledError.shouldRetry) {
        if (streamingService) {
          streamingService.log(
            'Continuing without dynamic content extraction...',
            'warn'
          );
        } else {
          context.logger.warn('Continuing without dynamic content extraction');
        }
        enhancedError = handledError;
      } else {
        throw handledError;
      }
    }
  }

  context.performanceMonitor.endPhase();
  if (streamingService) {
    streamingService.endPhase({
      success: true,
      dynamicContentPieces: dynamicContents.contents?.length || 0,
    });
  }

  // Step 3: Combine main content with dynamic content
  if (streamingService) {
    streamingService.startPhase(
      'content-processing',
      'Combining main content with dynamic content'
    );
  }
  context.performanceMonitor.startPhase('content processing');

  if (streamingService) {
    streamingService.updateProgress({
      message: 'Combining main content with extracted dynamic content',
    });
  }
  const combinedHtml = await combineContent(context.page, dynamicContents);
  await closeBrowser();

  if (streamingService) {
    streamingService.updateProgress({
      message: 'Cleaning and formatting HTML content',
    });
  }
  const cleanedHTML = await fixAndFormatHTML(combinedHtml);

  // Save HTML file only if outputHtmlFilename is provided (for backward compatibility)
  if (outputHtmlFilename) {
    await saveToFile(cleanedHTML, `${outputHtmlFilename}.html`);
  }

  // Step 4: Convert to markdown and improve with AI
  let finalMarkdown;
  if (userQuery) {
    if (streamingService) {
      streamingService.updateProgress({
        message:
          'Converting to markdown and improving with AI based on user query',
      });
    }
    finalMarkdown = await convertAndImproveMarkdownFromHTML(
      cleanedHTML,
      userQuery,
      streamingService
    );

    // Save improved markdown file only if outputHtmlFilename is provided
    if (outputHtmlFilename) {
      await saveToFile(finalMarkdown, `${outputHtmlFilename}.md`);
    }
  } else {
    if (streamingService) {
      streamingService.updateProgress({
        message: 'Converting HTML to markdown',
      });
    }
    finalMarkdown = await convertToMarkdown(cleanedHTML);

    // Save raw markdown file only if outputHtmlFilename is provided
    if (outputHtmlFilename) {
      await saveToFile(finalMarkdown, `${outputHtmlFilename}.raw.md`);
    }
  }

  context.performanceMonitor.endPhase();
  if (streamingService) {
    streamingService.endPhase({
      success: true,
      markdownLength: finalMarkdown?.length || 0,
      htmlLength: cleanedHTML?.length || 0,
    });
  }

  return {
    enhancedError,
    markdown: finalMarkdown,
    html: cleanedHTML,
  };
}

/**
 * Main scraper function that orchestrates the entire process
 * @param {string} url - The URL to scrape
 * @param {string} outputHtmlFilename - The filename to save the result as (default: "scraped")
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @returns {Promise<boolean>} - Success status
 */
export async function scrape(
  url,
  outputHtmlFilename = 'scraped',
  userQuery = '',
  mode = SCRAPING_MODES.AUTO
) {
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;

  while (retryCount < MAX_RETRY_COUNT && !success) {
    try {
      // Create scraping context
      context = createScrapingContext();
      context.currentMode = mode;

      context.logger.info(`Scraping ${url} (attempt ${retryCount + 1})`);

      // Start performance monitoring
      context.performanceMonitor.start();

      // Step 1: Initialize browser and get page
      context.performanceMonitor.startPhase('browser setup');
      const browserInitialized = await initBrowser();
      if (!browserInitialized) {
        throw new Error('Failed to initialize browser');
      }

      context.browser = getBrowser();
      context.page = getPage();

      // Set browser context for enhanced monitoring
      context.performanceMonitor.setBrowserContext(context.page);

      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      // Step 2: Navigate to URL and wait for page to stabilize
      console.log('------------------------------------------------------');
      context.performanceMonitor.startPhase('page loading');
      await navigateToUrl(context.page, url);
      await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      // Step 3: Process iframes by extracting their content
      console.log('------------------------------------------------------');
      context.performanceMonitor.startPhase('iframe processing');
      await handleIframes(context.page);
      context.performanceMonitor.endPhase();
      console.log('------------------------------------------------------');

      let result;
      if (context.currentMode === SCRAPING_MODES.NORMAL) {
        result = await processNormalMode(
          context,
          outputHtmlFilename,
          userQuery
        );
      } else {
        result = await processBeastMode(context, outputHtmlFilename, userQuery);
      }
      enhancedError = result.enhancedError;

      success = true;

      // Stop performance monitoring
      context.performanceMonitor.stop();

      // Show final status
      if (enhancedError) {
        context.logger.warn(
          `Scraping completed with limitations: ${enhancedError.userMessage}`
        );
      } else {
        context.logger.info(
          'Scraping completed successfully with all features!'
        );
      }
    } catch (error) {
      // Handle the error with our global error handler
      const handledError = await handleError(error, {
        operation: 'scrape',
        url,
        attempt: retryCount + 1,
        mode: context?.currentMode || mode,
        outputFile: outputHtmlFilename,
      });

      // Check if this error should stop retries
      if (
        !handledError.shouldRetry ||
        handledError.category === ERROR_CATEGORIES.RATE_LIMIT
      ) {
        console.log(
          `\n🛑 Scraping stopped - Error ID: ${handledError.stackId}`
        );
        break; // Don't retry for auth, rate limit, or non-retryable errors
      }

      retryCount++;

      if (retryCount < MAX_RETRY_COUNT) {
        const retryConfig = getRetryConfig(handledError);
        const delay =
          retryConfig.strategy === 'exponential'
            ? RETRY_DELAY * Math.pow(2, retryCount - 1)
            : RETRY_DELAY;

        console.log(
          `🔄 Retrying in ${delay}ms... (attempt ${
            retryCount + 1
          }/${MAX_RETRY_COUNT})`
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        console.log(
          `\n❌ Maximum retry attempts (${MAX_RETRY_COUNT}) reached - Error ID: ${handledError.stackId}`
        );
      }
    } finally {
      // Always close browser in finally block
      if (context && context.browser) {
        await closeBrowser();
      }

      // Stop performance monitoring if it was started
      if (
        context &&
        context.performanceMonitor &&
        context.performanceMonitor.monitoringInterval
      ) {
        context.performanceMonitor.stop();
      }
    }
  }

  return success;
}

/**
 * Streaming-enabled scraper function
 * @param {string} url - The URL to scrape
 * @param {string} outputHtmlFilename - The filename identifier (default: "scraped")
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @param {Function} progressCallback - Optional callback for streaming progress updates
 * @returns {Promise<Object>} - Structured result with markdown and HTML content
 */
export async function scrapeWithStreaming(
  url,
  outputHtmlFilename = 'scraped',
  userQuery = '',
  mode = SCRAPING_MODES.BEAST,
  progressCallback = null
) {
  const startTime = Date.now();
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;
  let finalMarkdown = null;
  let finalHtml = null;

  // Initialize streaming service
  const streamingService = createStreamingService(progressCallback);

  try {
    streamingService.log(`Starting scraping process for ${url}`, 'info');

    while (retryCount < MAX_RETRY_COUNT && !success) {
      try {
        streamingService.log(
          `Scraping ${url} (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
          'info'
        );

        // Create scraping context
        context = createScrapingContext();
        context.currentMode = mode;
        context.streamingService = streamingService;

        // Start performance monitoring
        context.performanceMonitor.start();

        // Step 1: Initialize browser and get page
        streamingService.startPhase(
          'browser-setup',
          'Initializing browser and setting up page'
        );
        context.performanceMonitor.startPhase('browser setup');

        const browserInitialized = await initBrowser();
        if (!browserInitialized) {
          throw new Error('Failed to initialize browser');
        }

        context.browser = getBrowser();
        context.page = getPage();

        // Set browser context for enhanced monitoring
        context.performanceMonitor.setBrowserContext(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true, browser: 'initialized' });

        // Step 2: Navigate to URL and wait for page to stabilize
        streamingService.startPhase(
          'page-loading',
          `Navigating to ${url} and waiting for page to load`
        );
        context.performanceMonitor.startPhase('page loading');

        await navigateToUrl(context.page, url);
        streamingService.updateProgress({
          message:
            'Page loaded, performing infinite scroll to load dynamic content',
        });

        await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true, url: url });

        // Step 3: Process iframes by extracting their content
        streamingService.startPhase(
          'iframe-processing',
          'Processing iframe content'
        );
        context.performanceMonitor.startPhase('iframe processing');

        await handleIframes(context.page);
        context.performanceMonitor.endPhase();
        streamingService.endPhase({ success: true });

        // Step 4: Execute scraping based on mode
        let scrapingResult;
        if (context.currentMode === SCRAPING_MODES.NORMAL) {
          scrapingResult = await processNormalMode(
            context,
            null, // Don't save files in streaming mode
            userQuery
          );
        } else {
          scrapingResult = await processBeastMode(
            context,
            null, // Don't save files in streaming mode
            userQuery
          );
        }

        enhancedError = scrapingResult.enhancedError;
        finalMarkdown = scrapingResult.markdown;
        finalHtml = scrapingResult.html;

        success = true;

        // Stop performance monitoring
        context.performanceMonitor.stop();

        // Log final status
        if (enhancedError) {
          streamingService.log('Scraping completed with limitations', 'warn');
          streamingService.log(
            `Enhanced features: ${enhancedError.userMessage}`,
            'warn'
          );
        } else {
          streamingService.log(
            'Scraping completed successfully with all features!',
            'info'
          );
        }
      } catch (error) {
        // Stream the error
        streamingService.streamError(error, {
          url,
          attempt: retryCount + 1,
          mode: context?.currentMode || mode,
        });

        // Handle the error with our global error handler
        const handledError = await handleError(error, {
          operation: 'scrapeWithStreaming',
          url,
          attempt: retryCount + 1,
          mode: context?.currentMode || mode,
          outputFile: outputHtmlFilename,
        });

        retryCount++;

        if (retryCount < MAX_RETRY_COUNT) {
          const retryConfig = getRetryConfig(handledError);
          const delay =
            retryConfig.strategy === 'exponential'
              ? RETRY_DELAY * Math.pow(2, retryCount - 1)
              : RETRY_DELAY;

          streamingService.log(
            `Retrying in ${delay}ms... (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
            'info'
          );
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          streamingService.log(
            `Maximum retry attempts (${MAX_RETRY_COUNT}) reached - Error ID: ${handledError.stackId}`,
            'error'
          );
        }
      } finally {
        // Always close browser in finally block
        if (context && context.browser) {
          await closeBrowser();
        }

        // Stop performance monitoring if it was started
        if (
          context &&
          context.performanceMonitor &&
          context.performanceMonitor.monitoringInterval
        ) {
          context.performanceMonitor.stop();
        }
      }
    }

    // Complete the streaming process
    const processingTime = Date.now() - startTime;
    const result = {
      success,
      markdown: finalMarkdown,
      html: finalHtml,
      processingTime,
      enhancedError,
    };

    streamingService.complete(result);
    return result;
  } catch (error) {
    streamingService.streamError(error);
    streamingService.log('Fatal error in scraping process', 'error');

    return {
      success: false,
      markdown: null,
      html: null,
      processingTime: Date.now() - startTime,
      error: error.message,
      enhancedError: null,
    };
  } finally {
    // Cleanup streaming service
    streamingService.cleanup();
  }
}
