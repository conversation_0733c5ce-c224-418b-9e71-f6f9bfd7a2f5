{"name": "context-scraper", "version": "1.0.0", "description": "A comprehensive web scraper built with <PERSON><PERSON> that extracts content from web pages, including dynamic elements revealed through AI-powered interaction detection", "main": "src/index.js", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node src/index.js", "help": "node src/index.js --help", "dev": "node src/index.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@hono/node-server": "^1.17.0", "ai": "^4.3.19", "cli-progress": "^3.12.0", "dotenv": "^17.2.0", "hono": "^4.8.5", "ora": "^8.2.0", "playwright-core": "^1.54.1", "rehype-format": "^5.0.1", "rehype-highlight": "^7.0.2", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-stringify": "^11.0.0", "unified": "^11.0.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.31.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "prettier": "^3.6.2"}}